[project]
name = "languagecoach"
version = "0.1.0"
description = "languagecoach using crewAI"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.14"
dependencies = [
    "crewai[tools]>=0.150.0,<1.0.0"
]

[project.scripts]
languagecoach = "languagecoach.main:run"
run_crew = "languagecoach.main:run"
train = "languagecoach.main:train"
replay = "languagecoach.main:replay"
test = "languagecoach.main:test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.crewai]
type = "crew"
